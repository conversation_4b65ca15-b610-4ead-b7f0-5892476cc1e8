import{d as w,r as h,a as y,b as _,e as l,j as e,w as d,h as v,u as o,E as x,x as g,f as p,D as a,o as k,G as C}from"./index-B2o9x5LI.js";const j={style:{"background-color":"#d2d2fa",height:"100vh",display:"grid","place-items":"center"}},E={style:{"background-color":"#fff",width:"90%",height:"80%","border-radius":"10px",padding:"20px",overflow:"auto"}},V={style:{display:"flex","justify-content":"space-between","align-items":"center"}},B={style:{display:"flex","justify-content":"space-between","align-items":"center"}},S={style:{"margin-top":"20px"}},N=w({__name:"WxinfoView",setup(D){const r=h([]),c=async()=>{try{r.value=await g.post("/api/ls/wxinfo")}catch(n){return console.error("Error fetching data:",n),[]}};y(c);const u=async()=>{await c()},f=()=>{const n=m(r.value);b(n,"wxinfo_data.csv")},m=n=>{const t=Object.keys(n[0]).join(","),s=n.map(i=>Object.values(i).join(","));return`${t}
${s.join(`
`)}`},b=(n,t)=>{const s=new Blob([new Uint8Array([239,187,191]),n],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");navigator.msSaveBlob?navigator.msSaveBlob(s,t):(i.href=URL.createObjectURL(s),i.setAttribute("download",t),document.body.appendChild(i),i.click(),document.body.removeChild(i)),C.success({title:"Success",message:"CSV file exported successfully!"})};return(n,t)=>{const s=v("el-button");return k(),_("div",j,[l("div",E,[l("div",V,[t[2]||(t[2]=l("div",{style:{"font-size":"20px","font-weight":"bold"}},"微信信息（已经登录）",-1)),l("div",B,[e(s,{style:{"margin-right":"10px"},onClick:u},{default:d(()=>t[0]||(t[0]=[p("刷新",-1)])),_:1,__:[0]}),e(s,{style:{"margin-right":"10px"},onClick:f},{default:d(()=>t[1]||(t[1]=[p("导出",-1)])),_:1,__:[1]})])]),l("div",S,[e(o(x),{data:r.value,style:{width:"100%"}},{default:d(()=>[e(o(a),{"min-width":30,prop:"pid",label:"进程id"}),e(o(a),{"min-width":40,prop:"version",label:"微信版本"}),e(o(a),{"min-width":40,prop:"account",label:"账号"}),e(o(a),{"min-width":45,prop:"mobile",label:"手机号"}),e(o(a),{"min-width":40,prop:"nickname",label:"昵称"}),e(o(a),{"min-width":30,prop:"mail",label:"邮箱"}),e(o(a),{"min-width":50,prop:"wxid",label:"微信原始id"}),e(o(a),{prop:"wx_dir",label:"微信文件夹路径"}),e(o(a),{prop:"key",label:"密钥(key)"})]),_:1},8,["data"])])])])}}});export{N as default};
