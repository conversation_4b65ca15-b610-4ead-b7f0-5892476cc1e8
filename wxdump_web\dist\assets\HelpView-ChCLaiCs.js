import{d as a,b as e,J as o,o as r}from"./index-B2o9x5LI.js";const b={class:"about"},n=a({__name:"HelpView",setup(m){return(s,t)=>(r(),e("div",b,t[0]||(t[0]=[o('<h1 id="-center-pywxdump-center-" style="text-align:center;"> PyWxDump UserGuide &amp; FAQ </h1><br><br><br><br><br><br><h2 style="text-align:center;"><a href="https://github.com/xaoyaoo/PyWxDump/blob/master/doc/UserGuide.md" target="_blank">使用教程</a><br><a href="https://github.com/xaoyaoo/PyWxDump/blob/master/doc/FAQ.md" target="_blank">常见问题</a><br><a href="https://github.com/xaoyaoo/PyWxDump/blob/master/doc/CE%E8%8E%B7%E5%8F%96%E5%9F%BA%E5%9D%80.md" target="_blank">CE获取基址</a><br><a href="https://github.com/xaoyaoo/PyWxDump/blob/master/doc/wx%E6%95%B0%E6%8D%AE%E5%BA%93%E7%AE%80%E8%BF%B0.md" target="_blank">wx数据库简述</a><br><a href="https://github.com/xaoyaoo/PyWxDump/blob/master/doc/MAC%E6%95%B0%E6%8D%AE%E5%BA%93%E8%A7%A3%E5%AF%86.md" target="_blank">MAC数据库解密</a></h2>',8)])))}});export{n as default};
