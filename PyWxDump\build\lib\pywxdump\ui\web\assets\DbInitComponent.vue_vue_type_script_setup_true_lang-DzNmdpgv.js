import{d as L,r as n,b as u,j as r,h as R,o as i,q as J,i as y,e as t,u as f,E as K,w,D as _,f as v,c as O,t as Q,K as E,L as T,x as j,H as D,I as z,N as X}from"./index-B2o9x5LI.js";const Z={class:"progress-bar"},$=L({__name:"ProgressBar",props:{startORstop:{type:Number,required:!0}},setup(I){const d=n(0),m=n(500),s=[{color:"#f56c6c",percentage:20},{color:"#e6a23c",percentage:40},{color:"#5cb87a",percentage:60},{color:"#1989fa",percentage:80},{color:"#6f7ad3",percentage:100}],b=n(new Date().getTime()),x=I,h=()=>{if(x.startORstop===1){d.value=100;return}b.value=new Date().getTime(),!(d.value>=99)&&(d.value>=60&&(m.value=m.value+50),d.value=d.value+1,setTimeout(h,m.value))};return h(),(a,k)=>{const p=R("el-progress");return i(),u("div",Z,[r(p,{type:"dashboard",percentage:d.value,color:s},null,8,["percentage"])])}}}),ee={style:{"background-color":"#d2d2fa",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},te={key:0},le={style:{"background-color":"#fff",width:"90%","min-width":"800px",height:"80%","border-radius":"10px",padding:"20px",overflow:"auto"}},ae={style:{"margin-top":"20px"}},ie={style:{"margin-top":"20px"}},oe={key:1},ne={key:0},se={key:1,style:{"background-color":"#fff",width:"90%","min-width":"800px",height:"80%","border-radius":"10px",padding:"20px",overflow:"auto"}},re={style:{"margin-top":"20px"}},ue={style:{"margin-top":"20px"}},de={key:2},pe={key:0},ve={key:1,style:{"background-color":"#fff",width:"80%","min-width":"800px",height:"70%","border-radius":"10px",padding:"20px",overflow:"auto"}},ce={style:{"margin-top":"20px"}},ye={key:0},fe={key:1},me={key:2},ge={key:3},xe={key:3,style:{display:"flex","justify-content":"space-between"}},we={style:{width:"200px",height:"150px","background-color":"#fff",display:"flex","flex-direction":"column","align-items":"center","border-radius":"10px","margin-right":"20px"}},_e={style:{width:"200px",height:"150px","background-color":"#fff",display:"flex","flex-direction":"column","align-items":"center","border-radius":"10px","margin-right":"20px"}},be={style:{width:"200px",height:"150px","background-color":"#fff",display:"flex","flex-direction":"column","align-items":"center","border-radius":"10px"}},ke=L({__name:"DbInitComponent",setup(I){const d=n(0),m=n(-1),s=n(""),b=n(!1),x=n([]),h=n(""),a=n(!1),k=n(!1),p=n("false"),V=n(""),g=n(""),M=n(""),c=n(""),B=n([]),S=l=>{l&&(localStorage.setItem("isDbInit","t"),X.push("/"),z({type:"success",message:"初始化成功！"}))},N=async()=>{if(a.value){console.log("正在解密中，请稍后再试！");return}a.value=!0;try{a.value=!0,m.value=0;let l={wx_path:g.value,key:M.value,my_wxid:c.value};const e=await j.post("/api/ls/init_key",l);b.value=e.is_init,e.is_init&&(d.value=100),a.value=!1,S(e.is_init)}catch(l){return d.value=0,k.value=!0,a.value=!1,D.alert(l,"error",{confirmButtonText:"确认",callback:e=>{z({type:"error",message:`action: ${e}`})}}),[]}a.value=!1},P=async()=>{try{let l={wx_path:g.value,merge_path:V.value,my_wxid:c.value};const e=await j.post("/api/ls/init_nokey",l);b.value=e.is_init,e.is_init&&(d.value=100),a.value=!1,S(e.is_init)}catch(l){return d.value=0,k.value=!0,a.value=!1,D.alert(l,"error",{confirmButtonText:"确认",callback:e=>{s.value=""}}),[]}a.value=!1},Y=async l=>{c.value=l.wxid},F=async()=>{try{const l=await j.post("/api/ls/init_last_local_wxid");B.value=l.local_wxids.map(e=>({wxid:e})),B.value.length===1&&(c.value=B.value[0].wxid,await q())}catch(l){return console.error("Error fetching data:",l),[]}},q=async()=>{try{let l={wx_path:g.value,merge_path:V.value,my_wxid:c.value};const e=await j.post("/api/ls/init_last",l);b.value=e.is_init,e.is_init?(d.value=100,a.value=!1,S(e.is_init)):(k.value=!0,a.value=!1,D.alert("未发现上次的设置数据！","error",{confirmButtonText:"确认",callback:C=>{s.value=""}})),a.value=!1}catch(l){return k.value=!0,a.value=!1,D.alert(l,"error",{confirmButtonText:"确认",callback:e=>{s.value=""}}),[]}a.value=!1},H=async()=>{try{x.value=await j.post("/api/ls/wxinfo"),x.value.length===1&&(W(x.value[0]),h.value=" (检测到只有一个微信，将在5秒后自动选择) ",setTimeout(G,5e3))}catch(l){return console.error("Error fetching data:",l),[]}},W=async l=>{V.value="",g.value=l.wx_dir,M.value=l.key,c.value=l.wxid},G=()=>{if(g.value===""&&M.value===""&&c.value===""){console.log("请填写完整信息! ");return}if(a.value){console.log("正在解密...，请稍后再试！");return}N()};return J(s,l=>{l==="auto"?H():l==="custom"||l==="last"&&F()}),(l,e)=>{const C=R("el-button"),A=R("el-divider"),U=R("el-input");return i(),u("div",ee,[s.value==="last"?(i(),u("div",te,[t("div",le,[e[10]||(e[10]=t("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}},[t("div",{style:{"font-size":"20px","font-weight":"bold"}},"选择要查看的微信")],-1)),t("div",ae,[r(f(K),{data:B.value,onCurrentChange:Y,"highlight-current-row":"",style:{width:"100%"}},{default:w(()=>[r(f(_),{"min-width":50,prop:"wxid",label:"微信原始id"})]),_:1},8,["data"])]),t("div",ie,[r(C,{style:{"margin-right":"10px","margin-top":"10px",width:"100%"},type:"success",onClick:q},{default:w(()=>e[9]||(e[9]=[v("确定 ",-1)])),_:1,__:[9]})])])])):s.value==="auto"?(i(),u("div",oe,[a.value?(i(),u("div",ne,[a.value?(i(),O($,{key:0,startORstop:m.value},null,8,["startORstop"])):y("",!0)])):(i(),u("div",se,[e[11]||(e[11]=t("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}},[t("div",{style:{"font-size":"20px","font-weight":"bold"}},"选择要查看的微信(会清空work下对应wxid数据)")],-1)),t("div",re,[r(f(K),{data:x.value,onCurrentChange:W,"highlight-current-row":"",style:{width:"100%"}},{default:w(()=>[r(f(_),{"min-width":30,prop:"pid",label:"进程id"}),r(f(_),{"min-width":40,prop:"version",label:"微信版本"}),r(f(_),{"min-width":40,prop:"account",label:"账号"}),r(f(_),{"min-width":40,prop:"nickname",label:"昵称"}),r(f(_),{"min-width":50,prop:"wxid",label:"微信原始id"})]),_:1},8,["data"])]),t("div",ue,[r(C,{style:{"margin-right":"10px","margin-top":"10px",width:"100%"},type:"success",onClick:G},{default:w(()=>[v("确定"+Q(h.value),1)]),_:1})])]))])):s.value==="custom"?(i(),u("div",de,[a.value?(i(),u("div",pe,[a.value?(i(),O($,{key:0,startORstop:m.value},null,8,["startORstop"])):y("",!0)])):(i(),u("div",ve,[e[26]||(e[26]=t("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}},[t("div",{style:{"font-size":"20px","font-weight":"bold"}},"自定义-文件位置"),t("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}})],-1)),t("div",ce,[E(t("input",{type:"radio","onUpdate:modelValue":e[0]||(e[0]=o=>p.value=o),value:"true"},null,512),[[T,p.value]]),e[20]||(e[20]=v(" 使用 KEY          ",-1)),E(t("input",{type:"radio","onUpdate:modelValue":e[1]||(e[1]=o=>p.value=o),value:"false"},null,512),[[T,p.value]]),e[21]||(e[21]=v(" 不使用 KEY ",-1)),p.value=="false"?(i(),u("div",ye,e[12]||(e[12]=[v(" 说明：1、表示数据库已解密并合并",-1),t("br",null,null,-1),v("2、合并后的数据库需要包含(MediaMSG,MSG,MicroMsg,OpenIMMsg)这些数据库合并的内容",-1),t("br",null,null,-1)]))):y("",!0),p.value=="true"?(i(),u("div",fe,e[13]||(e[13]=[v(" 说明：1、自动根据key解密微信文件夹下的数据库",-1),t("br",null,null,-1),v("2、必须保证key正确，否则解密失败",-1),t("br",null,null,-1)]))):y("",!0),r(A),p.value=="true"?(i(),u("div",me,[e[14]||(e[14]=t("label",null,"密钥key(必填): ",-1)),r(U,{placeholder:"密钥key (64位)",modelValue:M.value,"onUpdate:modelValue":e[2]||(e[2]=o=>M.value=o),style:{width:"80%"}},null,8,["modelValue"]),e[15]||(e[15]=t("br",null,null,-1))])):y("",!0),p.value=="false"?(i(),u("div",ge,[e[16]||(e[16]=t("label",null,"merge_all.db 文件路径(必填,非文件夹): ",-1)),r(U,{placeholder:"(MediaMSG.db,MSG.db,MicroMsg.db,OpenIMMsg.db)合并后的数据库",modelValue:V.value,"onUpdate:modelValue":e[3]||(e[3]=o=>V.value=o),style:{width:"80%"}},null,8,["modelValue"]),e[17]||(e[17]=t("br",null,null,-1))])):y("",!0),e[22]||(e[22]=t("label",null,"微信文件夹路径(必填): ",-1)),r(U,{placeholder:"C:\\***\\WeChat Files\\wxid_*******",modelValue:g.value,"onUpdate:modelValue":e[4]||(e[4]=o=>g.value=o),style:{width:"80%"}},null,8,["modelValue"]),e[23]||(e[23]=t("br",null,null,-1)),e[24]||(e[24]=t("label",null,"微信原始id(必填): ",-1)),r(U,{placeholder:"wxid_*******",modelValue:c.value,"onUpdate:modelValue":e[5]||(e[5]=o=>c.value=o),style:{width:"80%"}},null,8,["modelValue"]),e[25]||(e[25]=t("br",null,null,-1)),p.value=="true"?(i(),O(C,{key:4,style:{"margin-top":"10px",width:"100%"},type:"success",onClick:N},{default:w(()=>e[18]||(e[18]=[v(" 确定 ",-1)])),_:1,__:[18]})):y("",!0),p.value=="false"?(i(),O(C,{key:5,style:{"margin-top":"10px",width:"100%"},type:"success",onClick:P},{default:w(()=>e[19]||(e[19]=[v(" 确定 ",-1)])),_:1,__:[19]})):y("",!0)])]))])):s.value===""?(i(),u("div",xe,[t("label",we,[E(t("input",{type:"radio","onUpdate:modelValue":e[6]||(e[6]=o=>s.value=o),value:"last"},null,512),[[T,s.value]]),e[27]||(e[27]=t("div",{style:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center",height:"100%"}},[t("div",null,"使用历史数据")],-1))]),t("label",_e,[E(t("input",{type:"radio","onUpdate:modelValue":e[7]||(e[7]=o=>s.value=o),value:"auto"},null,512),[[T,s.value]]),e[28]||(e[28]=t("div",{style:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center",height:"100%"}},[t("div",null,"自动解密已登录微信")],-1))]),t("label",be,[E(t("input",{type:"radio","onUpdate:modelValue":e[8]||(e[8]=o=>s.value=o),value:"custom"},null,512),[[T,s.value]]),e[29]||(e[29]=t("div",{style:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center",height:"100%"}},[t("div",null,"自定义文件位置")],-1))])])):y("",!0)])}}});export{ke as _};
