import{_ as f}from"./DbInitComponent.vue_vue_type_script_setup_true_lang-DzNmdpgv.js";import{d as x,r as g,b as y,j as e,w as t,h as o,e as l,f as d,c as v,i as b,o as m}from"./index-B2o9x5LI.js";const w={class:"common-layout",style:{height:"100vh",width:"100%","background-color":"#F7F7F7"}},B=x({__name:"SettingView",setup(k){const s=g(""),c=i=>{s.value=i};return(i,n)=>{const r=o("el-header"),a=o("el-menu-item"),u=o("el-menu"),p=o("el-aside"),h=o("el-main"),_=o("el-container");return m(),y("div",w,[e(_,null,{default:t(()=>[e(r,{style:{height:"35px","max-height":"35px",width:"100%"}},{default:t(()=>n[0]||(n[0]=[l("h2",{style:{"text-align":"center"}},[d("欢迎使用"),l("a",{href:"https://github.com/xaoyaoo/PyWxDump.git"},"PyWxDump"),d("聊天记录查看工具! "),l("span",{style:{"font-size":"14px"}},"(如需提前体验更多功能请开通超级vip)")],-1)])),_:1,__:[0]}),e(_,{style:{height:"calc(100vh - 35px)",width:"100%"}},{default:t(()=>[e(p,{width:"200px",style:{height:"100%"}},{default:t(()=>[e(u,{style:{height:"100%","background-color":"#F7F7F7",color:"#262626"},"default-active":"2",class:"el-menu-vertical-demo",onSelect:c},{default:t(()=>[e(a,{index:"-1",disabled:""},{default:t(()=>n[1]||(n[1]=[l("span",{style:{color:"#043bea"}},"设置",-1)])),_:1,__:[1]}),e(a,{index:"db_init"},{default:t(()=>n[2]||(n[2]=[l("span",null,"初始化设置",-1)])),_:1,__:[2]}),e(a,{index:"3"},{default:t(()=>n[3]||(n[3]=[l("span",null,null,-1)])),_:1,__:[3]})]),_:1})]),_:1}),e(h,{style:{height:"100%","max-height":"100%",width:"100%",margin:"0",padding:"0"}},{default:t(()=>[s.value=="db_init"?(m(),v(f,{key:0})):b("",!0)]),_:1})]),_:1})]),_:1})])}}});export{B as default};
