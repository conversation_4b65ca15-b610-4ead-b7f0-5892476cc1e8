import type { PropType as __PropType } from 'vue';
declare const _sfc_main: import("vue").DefineComponent<{
    top: {
        type: __PropType<boolean | undefined>;
        required: false;
        default: boolean;
    };
    target: {
        type: __PropType<import('../types').Target | undefined>;
        required: false;
    };
    distance: {
        type: __PropType<number | undefined>;
        required: false;
        default: number;
    };
    identifier: {
        type: __PropType<any>;
        required: false;
    };
    firstload: {
        type: __PropType<boolean | undefined>;
        required: false;
        default: boolean;
    };
    slots: {
        type: __PropType<import('../types').Slots | undefined>;
        required: false;
    };
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "infinite"[], "infinite", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    top: {
        type: __PropType<boolean | undefined>;
        required: false;
        default: boolean;
    };
    target: {
        type: __PropType<import('../types').Target | undefined>;
        required: false;
    };
    distance: {
        type: __PropType<number | undefined>;
        required: false;
        default: number;
    };
    identifier: {
        type: __PropType<any>;
        required: false;
    };
    firstload: {
        type: __PropType<boolean | undefined>;
        required: false;
        default: boolean;
    };
    slots: {
        type: __PropType<import('../types').Slots | undefined>;
        required: false;
    };
}>> & {
    onInfinite?: ((...args: any[]) => any) | undefined;
}, {
    top: boolean | undefined;
    distance: number | undefined;
    firstload: boolean | undefined;
}, {}>;
export default _sfc_main;
