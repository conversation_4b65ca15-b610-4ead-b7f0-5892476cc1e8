import{d as r,b as a,e,f as t,o as s}from"./index-B2o9x5LI.js";/* empty css                                                                          *//* empty css                                                                       */const n={style:{"background-color":"#d2d2fa",height:"100vh",display:"grid","place-items":"center"}},m=r({__name:"CleanupView",setup(i){return(l,o)=>(s(),a("div",n,o[0]||(o[0]=[e("h2",{style:{"text-align":"center"}},[t("欢迎使用"),e("a",{href:"https://github.com/xaoyaoo/PyWxDump.git"},"PyWxDump"),t("聊天记录查看工具! ")],-1),e("h3",{style:{"text-align":"center"}},[t(" 微信存储空间清理，减少微信占用空间"),e("br"),t("通过选择某个人或群，把这群里的聊天记录中涉及的图片、视频、文件、语音等的媒体文件找出来"),e("br"),t(" 以群对话为单位有选择性的（比如时间段）或按群会话批量从电脑的缓存中清除。 ")],-1),e("h3",{style:{"text-align":"center"}},[t(" 打开电脑微信，点击左下角的菜单，选择设置->通用设置->存储空间管理->清理空间，即可查看微信占用的空间，点击清理即可清理微信占用的空间。"),e("br"),t(" 如果这些自带功能无法满足需要，请提交issue，我会增加点新的功能。 ")],-1),e("p",null,"如需提前体验更多功能，请多多支持，多多鼓励！",-1)])))}});export{m as default};
