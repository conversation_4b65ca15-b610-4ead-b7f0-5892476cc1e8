import{d as i,r as p,a as _,b as m,e as s,j as g,f,u as y,M as x,x as o,H as h,I as k,o as b}from"./index-B2o9x5LI.js";const w={class:"about"},M=i({__name:"AboutView",setup(v){const r=async()=>{try{const e=await o.post("/api/rs/check_update"),t=e.latest_version,c=e.msg,l=e.latest_url,u=`${c}：${t} 
 ${l||""}`;h.alert(u,"info",{confirmButtonText:"确认",callback:d=>{k({type:"info",message:`action: ${d}`})}})}catch{return[]}},a=p("# 加载中"),n=async()=>{try{const e=await o.post("/api/rs/get_readme");a.value=e}catch{return[]}};return _(()=>{n()}),(e,t)=>(b(),m("div",w,[s("h1",{id:"-center-pywxdump-center-",style:{"text-align":"center"}},[t[0]||(t[0]=f(" PyWxDump",-1)),s("a",{onClick:r,target:"_blank",style:{float:"right","margin-right":"30px"}},"检查更新")]),g(y(x),{source:a.value,style:{"background-color":"#d2d2fa"}},null,8,["source"])]))}});export{M as default};
