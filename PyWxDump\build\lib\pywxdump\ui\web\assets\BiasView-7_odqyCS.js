import{d as f,r as a,b as w,e,j as o,h as v,w as x,f as g,o as k,x as U}from"./index-B2o9x5LI.js";const B={style:{"background-color":"#d2d2fa",height:"100vh",display:"grid","place-items":"center"}},C={style:{"background-color":"#fff",width:"70%",height:"70%","border-radius":"10px",padding:"20px",overflow:"auto"}},E={style:{"margin-top":"20px"}},P=f({__name:"BiasView",setup(j){const r=a(""),i=a(""),p=a(""),d=a(""),s=a(""),n=a(""),y=async()=>{try{if(d.value===""&&s.value===""){n.value="key与wxdbPath必须填写一个";return}n.value=await U.post("/api/ls/biasaddr",{mobile:r.value,name:i.value,account:p.value,key:d.value,wxdbPath:s.value}),n.value=`{版本号:昵称,账号,手机号,邮箱,KEY}
`+n.value}catch(m){return n.value=`Error fetching data: 
`+m,console.error("Error fetching data:",m),[]}};return(m,l)=>{const u=v("el-input"),b=v("el-button"),V=v("el-divider");return k(),w("div",B,[e("div",C,[l[17]||(l[17]=e("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}},[e("div",{style:{"font-size":"20px","font-weight":"bold"}},"基址偏移"),e("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}})],-1)),e("div",E,[l[7]||(l[7]=e("label",null,"手机号: ",-1)),o(u,{placeholder:"请输入手机号",modelValue:r.value,"onUpdate:modelValue":l[0]||(l[0]=t=>r.value=t),style:{width:"80%"}},null,8,["modelValue"]),l[8]||(l[8]=e("br",null,null,-1)),l[9]||(l[9]=e("label",null,"昵称: ",-1)),o(u,{placeholder:"请输入昵称",modelValue:i.value,"onUpdate:modelValue":l[1]||(l[1]=t=>i.value=t),style:{width:"80%"}},null,8,["modelValue"]),l[10]||(l[10]=e("br",null,null,-1)),l[11]||(l[11]=e("label",null,"微信账号: ",-1)),o(u,{placeholder:"请输入微信号",modelValue:p.value,"onUpdate:modelValue":l[2]||(l[2]=t=>p.value=t),style:{width:"80%"}},null,8,["modelValue"]),l[12]||(l[12]=e("br",null,null,-1)),l[13]||(l[13]=e("label",null,"密钥（key）: ",-1)),o(u,{placeholder:"请输入密钥（key）（可选）",modelValue:d.value,"onUpdate:modelValue":l[3]||(l[3]=t=>d.value=t),style:{width:"80%"}},null,8,["modelValue"]),l[14]||(l[14]=e("br",null,null,-1)),l[15]||(l[15]=e("label",null,"微信数据库路径: ",-1)),o(u,{placeholder:"请输入微信数据库路径（可选）",modelValue:s.value,"onUpdate:modelValue":l[4]||(l[4]=t=>s.value=t),style:{width:"75%"}},null,8,["modelValue"]),l[16]||(l[16]=e("br",null,null,-1)),o(b,{style:{"margin-top":"10px",width:"50%"},type:"success",onClick:y},{default:x(()=>l[6]||(l[6]=[g("偏移",-1)])),_:1,__:[6]}),o(V),o(u,{type:"textarea",rows:10,readonly:"",placeholder:"输出结果",modelValue:n.value,"onUpdate:modelValue":l[5]||(l[5]=t=>n.value=t),style:{width:"100%",color:"#00bd7e"}},null,8,["modelValue"])])])])}}});export{P as default};
