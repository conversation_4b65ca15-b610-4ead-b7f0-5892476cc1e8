(function(r,e){typeof exports=="object"&&typeof module<"u"?e(exports,require("vue")):typeof define=="function"&&define.amd?define(["exports","vue"],e):(r=typeof globalThis<"u"?globalThis:r||self,e(r.V3InfiniteLoading={},r.Vue))})(this,function(r,e){"use strict";function y(t,i=null){if(!t)return!1;const o=t.getBoundingClientRect(),n=i?i.getBoundingClientRect():{top:0,left:0,bottom:window.innerHeight,right:window.innerWidth};return o.bottom>=n.top&&o.top<=n.bottom&&o.right>=n.left&&o.left<=n.right}async function h(t){return t?(await e.nextTick(),t.value instanceof HTMLElement?t.value:t.value?document.querySelector(t.value):null):null}function E(t){let i=`0px 0px ${t.distance}px 0px`;t.top&&(i=`${t.distance}px 0px 0px 0px`);const o=new IntersectionObserver(n=>{n[0].isIntersecting&&(t.firstload&&t.emit(),t.firstload=!0)},{root:t.parentEl,rootMargin:i});return t.infiniteLoading.value&&o.observe(t.infiniteLoading.value),o}async function p(t,i){if(await e.nextTick(),!t.top)return;const o=t.parentEl||document.documentElement;o.scrollTop=o.scrollHeight-i}const v="",u=(t,i)=>{const o=t.__vccOpts||t;for(const[n,c]of i)o[n]=c;return o},w={},S=t=>(e.pushScopeId("data-v-d3e37633"),t=t(),e.popScopeId(),t),x={class:"container"},V=[S(()=>e.createElementVNode("div",{class:"spinner"},null,-1))];function k(t,i){return e.openBlock(),e.createElementBlock("div",x,V)}const I=u(w,[["render",k],["__scopeId","data-v-d3e37633"]]),N={class:"state-error"},b=e.defineComponent({__name:"InfiniteLoading",props:{top:{type:Boolean,default:!1},target:{},distance:{default:0},identifier:{},firstload:{type:Boolean,default:!0},slots:{}},emits:["infinite"],setup(t,{emit:i}){const o=t;let n=null,c=0;const f=e.ref(null),s=e.ref(""),{top:B,firstload:T,distance:$}=o,{identifier:C,target:H}=e.toRefs(o),l={infiniteLoading:f,top:B,firstload:T,distance:$,parentEl:null,emit(){c=(l.parentEl||document.documentElement).scrollHeight,_.loading(),i("infinite",_)}},_={loading(){s.value="loading"},async loaded(){s.value="loaded",await p(l,c),y(f.value,l.parentEl)&&l.emit()},async complete(){s.value="complete",await p(l,c),n==null||n.disconnect()},error(){s.value="error"}};function m(){n==null||n.disconnect(),n=E(l)}return e.watch(C,m),e.onMounted(async()=>{l.parentEl=await h(H),m()}),e.onUnmounted(()=>n==null?void 0:n.disconnect()),(d,g)=>(e.openBlock(),e.createElementBlock("div",{ref_key:"infiniteLoading",ref:f,class:"v3-infinite-loading"},[e.withDirectives(e.createElementVNode("div",null,[e.renderSlot(d.$slots,"spinner",{},()=>[e.createVNode(I)],!0)],512),[[e.vShow,s.value=="loading"]]),s.value=="complete"?e.renderSlot(d.$slots,"complete",{key:0},()=>{var a;return[e.createElementVNode("span",null,e.toDisplayString(((a=d.slots)==null?void 0:a.complete)||"No more results!"),1)]},!0):e.createCommentVNode("",!0),s.value=="error"?e.renderSlot(d.$slots,"error",{key:1,retry:l.emit},()=>{var a;return[e.createElementVNode("span",N,[e.createElementVNode("span",null,e.toDisplayString(((a=d.slots)==null?void 0:a.error)||"Oops something went wrong!"),1),e.createElementVNode("button",{class:"retry",onClick:g[0]||(g[0]=(...O)=>l.emit&&l.emit(...O))},"retry")])]},!0):e.createCommentVNode("",!0)],512))}}),R="",L=u(b,[["__scopeId","data-v-4bdee133"]]);r.default=L,Object.defineProperties(r,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
