import{d as y,r as i,b as f,e as t,j as o,h as u,w as b,f as g,o as w,x as c}from"./index-B2o9x5LI.js";const x={style:{"background-color":"#d2d2fa",height:"100vh",display:"grid","place-items":"center"}},V={style:{"background-color":"#fff",width:"70%",height:"70%","border-radius":"10px",padding:"20px",overflow:"auto"}},h={style:{"margin-top":"20px"}},C=y({__name:"MergeView",setup(_){const a=i(""),s=i(""),n=i(""),p=async()=>{try{n.value=await c.post("/api/ls/merge",{dbPath:a.value,outPath:s.value})}catch(d){return n.value=`Error fetching data: 
`+d,console.error("Error fetching data:",d),[]}};return(d,e)=>{const r=u("el-input"),m=u("el-button"),v=u("el-divider");return w(),f("div",x,[t("div",V,[e[8]||(e[8]=t("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}},[t("div",{style:{"font-size":"20px","font-weight":"bold"}},"合并-微信数据库"),t("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}})],-1)),t("div",h,[e[4]||(e[4]=t("label",null,"数据库路径: ",-1)),o(r,{placeholder:"数据库路径（文件夹，并且确保文件夹下的db文件已经解密）：",modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=l=>a.value=l),style:{width:"80%"}},null,8,["modelValue"]),e[5]||(e[5]=t("br",null,null,-1)),e[6]||(e[6]=t("label",null,"微信数据库路径: ",-1)),o(r,{placeholder:"输出合并后的数据库路径",modelValue:s.value,"onUpdate:modelValue":e[1]||(e[1]=l=>s.value=l),style:{width:"80%"}},null,8,["modelValue"]),e[7]||(e[7]=t("br",null,null,-1)),o(m,{style:{"margin-top":"10px",width:"50%"},type:"success",onClick:p},{default:b(()=>e[3]||(e[3]=[g("合并",-1)])),_:1,__:[3]}),o(v),o(r,{type:"textarea",rows:10,readonly:"",placeholder:"合并后数据库路径",modelValue:n.value,"onUpdate:modelValue":e[2]||(e[2]=l=>n.value=l),style:{width:"100%"}},null,8,["modelValue"])])])])}}});export{C as default};
