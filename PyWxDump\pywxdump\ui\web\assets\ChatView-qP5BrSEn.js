import{d as C,h as p,b,o as s,e as l,i as c,j as r,w as a,f as u,t as m,c as V,u as S,k as T,_ as F,r as _,a as z,l as A,m as h,n as H,p as P,q as I,F as O,s as K,v as J,x as j,y as X,C as G,g as Q,z as Y}from"./index-B2o9x5LI.js";/* empty css                                                                          *//* empty css                                                                       */import{_ as Z}from"./IndexView.vue_vue_type_script_setup_true_lang-nwd8pAHf.js";const ee={key:0,style:{"max-width":"560px"}},te={key:0},le={key:1,style:{"max-width":"560px"}},ne={key:0},oe={key:1},re={key:2},ue={key:3},se={key:4},ie={key:5},ae={key:6},de={key:7},xe={key:8},fe={key:9},pe={key:10},me={key:11},we=C({__name:"UserInfoShow",props:{userinfo:{type:Object,default:()=>({})},show_all:{type:Boolean,default:!0,required:!1}},setup(t){const x=t,n=({row:f,rowIndex:e})=>(console.log(f.wxid,x.userinfo.extra.owner.wxid,f.wxid==x.userinfo.extra.owner.wxid),f.wxid==x.userinfo.extra.owner.wxid?(console.log("table-success-row"),"table-success-row"):"");return(f,e)=>{const o=p("el-divider"),d=p("el-image"),i=p("el-table-column"),v=p("el-table");return t.show_all?(s(),b("div",ee,[l("div",null,[r(o,{"content-position":"center"},{default:a(()=>e[0]||(e[0]=[u("基本信息",-1)])),_:1,__:[0]}),l("span",null,[u("wxid："+m(t.userinfo.wxid),1),e[1]||(e[1]=l("br",null,null,-1))]),l("span",null,[u("账号："+m(t.userinfo.account),1),e[2]||(e[2]=l("br",null,null,-1))]),l("span",null,[u("昵称："+m(t.userinfo.nickname),1),e[3]||(e[3]=l("br",null,null,-1))]),l("span",null,[u("备注："+m(t.userinfo.remark),1),e[4]||(e[4]=l("br",null,null,-1))])]),l("div",null,[r(o,{"content-position":"center"},{default:a(()=>e[5]||(e[5]=[u("账号信息",-1)])),_:1,__:[5]}),l("span",null,[u("性别："+m(t.userinfo.ExtraBuf["性别[1男2女]"]==1?"男":t.userinfo.ExtraBuf["性别[1男2女]"]==2?"女":""),1),e[6]||(e[6]=l("br",null,null,-1))]),l("span",null,[u("手机："+m(t.userinfo.ExtraBuf.手机号),1),e[7]||(e[7]=l("br",null,null,-1))]),l("span",null,[u("标签："+m(t.userinfo.LabelIDList.join("/")),1),e[8]||(e[8]=l("br",null,null,-1))]),l("span",null,[u("描述："+m(t.userinfo.describe),1),e[9]||(e[9]=l("br",null,null,-1))]),l("span",null,[u("个签："+m(t.userinfo.ExtraBuf.个性签名),1),e[10]||(e[10]=l("br",null,null,-1))]),l("span",null,[u("国家："+m(t.userinfo.ExtraBuf.国),1),e[11]||(e[11]=l("br",null,null,-1))]),l("span",null,[u("省份："+m(t.userinfo.ExtraBuf.省),1),e[12]||(e[12]=l("br",null,null,-1))]),l("span",null,[u("市名："+m(t.userinfo.ExtraBuf.市),1),e[13]||(e[13]=l("br",null,null,-1))])]),l("div",null,[r(o,{"content-position":"center"},{default:a(()=>e[14]||(e[14]=[u("其他信息",-1)])),_:1,__:[14]}),l("span",null,[u("公司："+m(t.userinfo.ExtraBuf.公司名称),1),e[15]||(e[15]=l("br",null,null,-1))]),l("span",null,[u("企微："+m(t.userinfo.ExtraBuf.企微属性),1),e[16]||(e[16]=l("br",null,null,-1))]),e[17]||(e[17]=l("span",null,[u("朋友圈背景："),l("br")],-1)),t.userinfo.ExtraBuf.朋友圈背景?(s(),V(d,{key:0,src:S(T)(t.userinfo.ExtraBuf.朋友圈背景),alt:"朋友圈背景",style:{"max-width":"200px","max-height":"200px"},"preview-src-list":[S(T)(t.userinfo.ExtraBuf.朋友圈背景)],"hide-on-click-modal":!0},null,8,["src","preview-src-list"])):c("",!0)]),t.userinfo.extra?(s(),b("div",te,[r(o,{"content-position":"center"},{default:a(()=>e[18]||(e[18]=[u("群聊信息",-1)])),_:1,__:[18]}),l("span",null,[u("群主: "+m(t.userinfo.extra.owner.wxid),1),e[19]||(e[19]=l("br",null,null,-1))]),e[20]||(e[20]=l("span",null,[u("群成员："),l("br")],-1)),r(v,{data:Object.values(t.userinfo.extra.wxid2userinfo),style:{width:"100%"},"row-class-name":n},{default:a(()=>[r(i,{prop:"wxid",label:"wxid"}),r(i,{prop:"account",label:"账号"}),r(i,{prop:"nickname",label:"昵称"}),r(i,{prop:"remark",label:"备注"}),r(i,{prop:"roomNickname",label:"群昵称"})]),_:1},8,["data"])])):c("",!0)])):(s(),b("div",le,[l("span",null,[u("wxid："+m(t.userinfo.wxid),1),e[21]||(e[21]=l("br",null,null,-1))]),l("span",null,[u("账号："+m(t.userinfo.account),1),e[22]||(e[22]=l("br",null,null,-1))]),l("span",null,[u("昵称："+m(t.userinfo.nickname),1),e[23]||(e[23]=l("br",null,null,-1))]),t.userinfo.remark?(s(),b("span",ne,[u("备注："+m(t.userinfo.remark),1),e[24]||(e[24]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf["性别[1男2女]"]?(s(),b("span",oe,[u("性别："+m(t.userinfo.ExtraBuf["性别[1男2女]"]==1?"男":t.userinfo.ExtraBuf["性别[1男2女]"]==2?"女":""),1),e[25]||(e[25]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.手机号?(s(),b("span",re,[u("手机："+m(t.userinfo.ExtraBuf.手机号),1),e[26]||(e[26]=l("br",null,null,-1))])):c("",!0),t.userinfo.LabelIDList&&t.userinfo.LabelIDList.length>0?(s(),b("span",ue,[u("标签："+m(t.userinfo.LabelIDList.join("/")),1),e[27]||(e[27]=l("br",null,null,-1))])):c("",!0),t.userinfo.describe?(s(),b("span",se,[u("描述："+m(t.userinfo.describe),1),e[28]||(e[28]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.个性签名?(s(),b("span",ie,[u("个签："+m(t.userinfo.ExtraBuf.个性签名),1),e[29]||(e[29]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.国?(s(),b("span",ae,[u("国家："+m(t.userinfo.ExtraBuf.国),1),e[30]||(e[30]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.省?(s(),b("span",de,[u("省份："+m(t.userinfo.ExtraBuf.省),1),e[31]||(e[31]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.市?(s(),b("span",xe,[u("市名："+m(t.userinfo.ExtraBuf.市),1),e[32]||(e[32]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.公司名称?(s(),b("span",fe,[u("公司："+m(t.userinfo.ExtraBuf.公司名称),1),e[33]||(e[33]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.企微属性?(s(),b("span",pe,[u("企微："+m(t.userinfo.ExtraBuf.企微属性),1),e[34]||(e[34]=l("br",null,null,-1))])):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.朋友圈背景?(s(),b("span",me,e[35]||(e[35]=[u("朋友圈背景：",-1),l("br",null,null,-1)]))):c("",!0),t.userinfo.ExtraBuf&&t.userinfo.ExtraBuf.朋友圈背景?(s(),V(d,{key:12,src:S(T)(t.userinfo.ExtraBuf.朋友圈背景),style:{"max-width":"200px","max-height":"200px"},alt:"朋友圈背景"},null,8,["src"])):c("",!0)]))}}}),W=F(we,[["__scopeId","data-v-a0244981"]]),ve={style:{padding:"10px 10px"}},ye={slot:"content",class:"tips"},be={key:0,style:{color:"#909399","font-size":"12px"}},ce=C({__name:"ContactsList",emits:["wxid"],setup(t,{emit:x}){const n=_([]);z(async()=>{try{n.value=await H()}catch(v){return console.error("Error fetching data:",v),[]}});const e=_(""),o=async()=>{try{if(e.value===""){n.value=await H();return}console.log(e.value),n.value=[];const v=await P(e.value);v!==null&&typeof v=="object"&&Object.entries(v).forEach(([y,k])=>{n.value.push(k)})}catch(v){return console.error("Error fetching data:",v),[]}},d=x,i=v=>{v!==void 0&&v.wxid!==void 0&&(console.log("wxid:",v.wxid),d("wxid",v.wxid))};return(v,y)=>{const k=p("el-input"),B=p("el-button"),R=p("el-avatar"),U=p("el-table-column"),E=p("el-tooltip"),w=p("el-table");return s(),b("div",null,[l("div",ve,[r(k,{placeholder:"请输入关键字",modelValue:e.value,"onUpdate:modelValue":y[0]||(y[0]=g=>e.value=g),onKeyup:A(o,["enter"]),style:{width:"170px","margin-left":"15px"}},null,8,["modelValue"]),r(B,{type:"primary",onClick:o,style:{width:"50px"}},{default:a(()=>y[1]||(y[1]=[u("搜索",-1)])),_:1,__:[1]})]),r(w,{data:n.value,stripe:"",style:{width:"100%"},"max-height":"100%",height:"100%","highlight-current-row":"",loading:"lazy",onCurrentChange:i},{default:a(()=>[r(U,{width:"57"},{default:a(({row:g})=>[g.headImgUrl!==""?(s(),V(R,{key:0,size:33,src:S(T)(g.headImgUrl)},null,8,["src"])):(s(),V(R,{key:1,size:33},{default:a(()=>y[2]||(y[2]=[u("群",-1)])),_:1,__:[2]}))]),_:1}),r(U,{width:"190"},{default:a(({row:g})=>[r(E,{class:"item",effect:"light",placement:"right"},{content:a(()=>[r(W,{userinfo:g,show_all:!1,style:{"max-width":"600px"}},null,8,["userinfo"])]),default:a(()=>[l("div",ye,[l("span",null,m(S(h)(g)),1),y[3]||(y[3]=u()),y[4]||(y[4]=l("br",null,null,-1)),g.nTime?(s(),b("span",be,m(g.nTime),1)):c("",!0)])]),_:2},1024)]),_:1})]),_:1},8,["data"])])}}}),ge=F(ce,[["__scopeId","data-v-0836fbf7"]]),_e=C({__name:"ChatRecprdsHeader",props:{wxid:{type:String,required:!0}},emits:["exporting"],setup(t,{emit:x}){const n=t,f=_(0),e=_({wxid:"",nOrder:0,nUnReadCount:0,strNickName:"",nStatus:0,nIsSend:0,strContent:"",nMsgLocalID:0,nMsgStatus:0,nTime:"",nMsgType:0,nMsgSubType:0,nickname:"",remark:"",account:"",describe:"",headImgUrl:"",ExtraBuf:{个性签名:"",企微属性:"",公司名称:"",国:"",备注图片:"",备注图片2:"",市:"","性别[1男2女]":0,手机号:"",朋友圈背景:"",省:""},LabelIDList:[],extra:null}),o=async()=>{var E,w,g,L,M,N,$,D;try{const q=await P("",[n.wxid]);return e.value.wxid=n.wxid,e.value.remark=(E=q[n.wxid])==null?void 0:E.remark,e.value.account=(w=q[n.wxid])==null?void 0:w.account,e.value.describe=(g=q[n.wxid])==null?void 0:g.describe,e.value.headImgUrl=(L=q[n.wxid])==null?void 0:L.headImgUrl,e.value.nickname=(M=q[n.wxid])==null?void 0:M.nickname,e.value.LabelIDList=(N=q[n.wxid])==null?void 0:N.LabelIDList,e.value.ExtraBuf=($=q[n.wxid])==null?void 0:$.ExtraBuf,e.value.extra=(D=q[n.wxid])==null?void 0:D.extra,q}catch(q){return console.error("Error fetching data wxid2user :",q),[]}},d=async()=>{try{f.value=0;const E=await J(n.wxid);return f.value=E||0,E}catch(E){return console.error("Error fetching data msg_count:",E),[]}},i=()=>{B.value=!1,o(),d()};z(()=>{console.log("ChatRecprdsHeader onMounted",n.wxid),i()}),I(()=>n.wxid,async(E,w)=>{E!==w&&i()});const v=_(!1),y=_(!1),k=async()=>{if(y.value){console.log("正在获取实时消息，请稍后再试!");return}y.value=!0;try{const E=await K();return y.value=!1,E}catch{return y.value=!1,[]}},B=_(!1),R=x,U=E=>{R("exporting",E),B.value=E};return(E,w)=>{const g=p("el-text"),L=p("el-col"),M=p("el-row"),N=p("el-dialog");return s(),b(O,null,[r(M,{gutter:5,style:{width:"100%"}},{default:a(()=>[r(L,{span:6,style:{"white-space":"nowrap"}},{default:a(()=>{var $;return[r(g,{class:"label_color mx-1",truncated:""},{default:a(()=>w[5]||(w[5]=[u("wxid:",-1)])),_:1,__:[5]}),w[6]||(w[6]=u("  ",-1)),r(g,{class:"data_color mx-1",truncated:"",title:($=e.value)==null?void 0:$.wxid},{default:a(()=>{var D;return[u(m((D=e.value)==null?void 0:D.wxid),1)]}),_:1},8,["title"])]}),_:1,__:[6]}),r(L,{span:6,style:{"white-space":"nowrap"}},{default:a(()=>[r(g,{class:"label_color mx-1",truncated:""},{default:a(()=>w[7]||(w[7]=[u("名称:",-1)])),_:1,__:[7]}),w[8]||(w[8]=u("  ",-1)),r(g,{class:"data_color mx-1",truncated:"",title:"show_name"},{default:a(()=>[u(m(S(h)(e.value)),1)]),_:1})]),_:1,__:[8]}),r(L,{span:5,style:{"white-space":"nowrap"}},{default:a(()=>[r(g,{class:"label_color mx-1",truncated:""},{default:a(()=>w[9]||(w[9]=[u("数量:",-1)])),_:1,__:[9]}),w[10]||(w[10]=u("  ",-1)),r(g,{class:"data_color mx-1",truncated:"",title:f.value},{default:a(()=>[u(m(f.value),1)]),_:1},8,["title"])]),_:1,__:[10]}),r(L,{span:2,style:{"white-space":"nowrap"}},{default:a(()=>[r(g,{class:"button_color mx-1 underline",truncated:"",onClick:w[0]||(w[0]=$=>v.value=!v.value)},{default:a(()=>w[11]||(w[11]=[u(" 详细信息",-1)])),_:1,__:[11]})]),_:1}),r(L,{span:2,style:{"white-space":"nowrap"}},{default:a(()=>[B.value?c("",!0):(s(),V(g,{key:0,class:"button_color mx-1 underline",truncated:"",onClick:w[1]||(w[1]=$=>{U(!0)})},{default:a(()=>w[12]||(w[12]=[u("导出备份 ",-1)])),_:1,__:[12]})),B.value?(s(),V(g,{key:1,class:"button_color mx-1 underline",truncated:"",onClick:w[2]||(w[2]=$=>{U(!1)})},{default:a(()=>w[13]||(w[13]=[u("聊天查看 ",-1)])),_:1,__:[13]})):c("",!0)]),_:1}),r(L,{span:3,style:{"white-space":"nowrap"}},{default:a(()=>[r(g,{class:"button_color mx-1 underline",truncated:"",onClick:w[3]||(w[3]=$=>{k()})},{default:a(()=>[w[14]||(w[14]=u("实时消息 ",-1)),y.value?(s(),b(O,{key:0},[u("...")],64)):c("",!0)]),_:1,__:[14]})]),_:1})]),_:1}),r(N,{modelValue:v.value,"onUpdate:modelValue":w[4]||(w[4]=$=>v.value=$),title:"详细信息",width:"600",center:""},{default:a(()=>[r(W,{userinfo:e.value,show_all:!0},null,8,["userinfo"])]),_:1},8,["modelValue"])],64)}}}),Ee=F(_e,[["__scopeId","data-v-b3b47fb2"]]),ke={style:{position:"relative"}},Be=C({__name:"ExportENDB",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(o,d)=>{console.log(o)});const n=_(""),f=_(""),e=async()=>{f.value="请求中...";try{f.value=await j.post("/api/rs/export_endb",{wx_path:n.value})}catch(o){return console.error("Error fetching data msg_count:",o),f.value=`请求失败
`+o,[]}};return(o,d)=>{const i=p("el-input"),v=p("el-button"),y=p("el-divider");return s(),b("div",null,[d[4]||(d[4]=u(" 微信文件夹路径(可选)： ",-1)),r(i,{placeholder:"微信文件夹路径[可为空,空表示使用默认的，无默认会报错](eg: C:\\****\\WeChat Files\\wxid_**** )",modelValue:n.value,"onUpdate:modelValue":d[0]||(d[0]=k=>n.value=k),style:{width:"70%"}},null,8,["modelValue"]),d[5]||(d[5]=l("br",null,null,-1)),d[6]||(d[6]=l("br",null,null,-1)),l("div",ke,[r(v,{type:"primary",onClick:d[1]||(d[1]=k=>e())},{default:a(()=>d[3]||(d[3]=[u("导出",-1)])),_:1,__:[3]})]),r(y),r(i,{type:"textarea",rows:6,readonly:"",placeholder:"",modelValue:f.value,"onUpdate:modelValue":d[2]||(d[2]=k=>f.value=k),style:{width:"100%"}},null,8,["modelValue"])])}}}),Ve={style:{position:"relative"}},Ce=C({__name:"ExportDEDB",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(d,i)=>{console.log(d)});const n=_(""),f=_(""),e=_(""),o=async()=>{e.value="正在处理中...";try{e.value=await j.post("/api/rs/export_dedb",{key:f.value,wx_path:n.value})}catch(d){return console.error("Error fetching data msg_count:",d),e.value=`请求失败
`+d,[]}};return(d,i)=>{const v=p("el-input"),y=p("el-button"),k=p("el-divider");return s(),b("div",null,[i[5]||(i[5]=u(" 密钥(可选)： ",-1)),r(v,{placeholder:"密钥[可为空,空表示使用默认的，无默认会报错]",modelValue:f.value,"onUpdate:modelValue":i[0]||(i[0]=B=>f.value=B),style:{width:"75%"}},null,8,["modelValue"]),i[6]||(i[6]=l("br",null,null,-1)),i[7]||(i[7]=l("br",null,null,-1)),i[8]||(i[8]=u(" 微信文件夹路径(可选)： ",-1)),r(v,{placeholder:"微信文件夹路径[可为空,空表示使用默认的，无默认会报错](eg: C:\\****\\WeChat Files\\wxid_**** )",modelValue:n.value,"onUpdate:modelValue":i[1]||(i[1]=B=>n.value=B),style:{width:"70%"}},null,8,["modelValue"]),i[9]||(i[9]=l("br",null,null,-1)),i[10]||(i[10]=l("br",null,null,-1)),l("div",Ve,[r(y,{type:"primary",onClick:i[2]||(i[2]=B=>o())},{default:a(()=>i[4]||(i[4]=[u("导出",-1)])),_:1,__:[4]})]),r(k),r(v,{type:"textarea",rows:6,readonly:"",placeholder:"",modelValue:e.value,"onUpdate:modelValue":i[3]||(i[3]=B=>e.value=B),style:{width:"100%"}},null,8,["modelValue"])])}}}),$e={style:{position:"relative"}},qe=C({__name:"ExportCSV",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(e,o)=>{console.log(e)});const n=_(""),f=async()=>{n.value="正在处理中...";try{n.value=await j.post("/api/rs/export_csv",{wxid:x.wxid})}catch(e){return console.error("Error fetching data msg_count:",e),n.value=`请求失败
`+e,[]}};return(e,o)=>{const d=p("el-button"),i=p("el-divider"),v=p("el-input");return s(),b("div",null,[l("div",$e,[r(d,{type:"primary",onClick:o[0]||(o[0]=y=>f())},{default:a(()=>o[2]||(o[2]=[u("导出",-1)])),_:1,__:[2]})]),r(i),r(v,{type:"textarea",rows:6,readonly:"",placeholder:"",modelValue:n.value,"onUpdate:modelValue":o[1]||(o[1]=y=>n.value=y),style:{width:"100%"}},null,8,["modelValue"])])}}}),Ie={style:{position:"relative"}},Le=C({__name:"ExportJSON",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(e,o)=>{console.log(e)}),_([]);const n=_(""),f=async()=>{n.value="正在处理中...";try{n.value=await j.post("/api/rs/export_json",{wxid:x.wxid})}catch(e){return console.error("Error fetching data msg_count:",e),n.value=`请求失败
`+e,[]}};return(e,o)=>{const d=p("el-button"),i=p("el-divider"),v=p("el-input");return s(),b("div",null,[l("div",Ie,[r(d,{type:"primary",onClick:o[0]||(o[0]=y=>f())},{default:a(()=>o[2]||(o[2]=[u("导出",-1)])),_:1,__:[2]})]),r(i),r(v,{type:"textarea",rows:6,readonly:"",placeholder:"",modelValue:n.value,"onUpdate:modelValue":o[1]||(o[1]=y=>n.value=y),style:{width:"100%"}},null,8,["modelValue"])])}}}),Se={style:{position:"relative"}},Ue=C({__name:"ExportHTML",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(e,o)=>{console.log(e)}),_([]);const n=_(""),f=async()=>{n.value="正在处理中...";try{n.value=await j.post("/api/rs/export_html",{wxid:x.wxid})}catch(e){return console.error("Error fetching data msg_count:",e),n.value=`请求失败
`+e,[]}};return(e,o)=>{const d=p("el-button"),i=p("el-divider"),v=p("el-input");return s(),b("div",null,[o[3]||(o[3]=l("span",null,"使用说明：（1）根据 https://blog.csdn.net/meser88/article/details/130229417 进行设置",-1)),o[4]||(o[4]=l("br",null,null,-1)),o[5]||(o[5]=l("span",null,"（2）打开导出的文件夹位置，使用（1）设置的浏览器打开 index.html 文件",-1)),l("div",Se,[r(d,{type:"primary",onClick:o[0]||(o[0]=y=>f())},{default:a(()=>o[2]||(o[2]=[u("导出",-1)])),_:1,__:[2]})]),r(i),r(v,{type:"textarea",rows:6,readonly:"",placeholder:"",modelValue:n.value,"onUpdate:modelValue":o[1]||(o[1]=y=>n.value=y),style:{width:"100%"}},null,8,["modelValue"])])}}}),De=C({__name:"ExportPDF",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(f,e)=>{console.log(f)});const n=()=>{console.log("requestExport")};return(f,e)=>{const o=p("el-button");return s(),b("div",null,[l("span",null,m(x.wxid),1),e[2]||(e[2]=l("br",null,null,-1)),r(o,{type:"primary",onClick:e[0]||(e[0]=d=>n())},{default:a(()=>e[1]||(e[1]=[u("导出",-1)])),_:1,__:[1]})])}}}),je=C({__name:"ExportDOCX",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(f,e)=>{console.log(f)});const n=()=>{console.log("requestExport")};return(f,e)=>{const o=p("el-button");return s(),b("div",null,[l("span",null,m(x.wxid),1),e[2]||(e[2]=l("br",null,null,-1)),r(o,{type:"primary",onClick:e[0]||(e[0]=d=>n())},{default:a(()=>e[1]||(e[1]=[u("导出",-1)])),_:1,__:[1]})])}}}),Re={id:"chat_export",style:{"background-color":"#d2d2fa",padding:"0"}},Me={style:{"background-color":"#d2d2fa",height:"calc(100vh - 65px)",display:"grid","place-items":"center"}},Ne={style:{"background-color":"#fff",width:"70%",height:"70%","border-radius":"10px",padding:"20px",overflow:"auto"}},Te={style:{"margin-top":"20px"}},ze={key:0},Oe=C({__name:"ChatExportMain",props:{wxid:{type:String,required:!0}},setup(t){const x=t;I(()=>x.wxid,(e,o)=>{console.log(e)});const n=_("");_("");const f={endb:{brief:"加密文件",detail:"导出的内容为微信加密数据库。可还原回微信,但会覆盖微信后续消息。(全程不解密，所以数据安全)"},dedb:{brief:"解密文件",detail:"导出的文件为解密后的sqlite数据库，并且会自动合并msg和media数据库为同一个，但是无法还原回微信。"},csv:{brief:"csv",detail:"只包含文本，但是可以用excel软件（wps，office）打开。"},json:{brief:"json",detail:"只包含文本，可用于数据分析，情感分析等方面。"},html:{brief:"html-测试中",detail:"主要用于浏览器可视化查看。"},pdf:{brief:"pdf-开发中",detail:"pdf版本。"},docx:{brief:"docx-开发中",detail:"docx版本。"}};return(e,o)=>{const d=p("el-option"),i=p("el-select"),v=p("el-divider"),y=p("el-main");return s(),b("div",Re,[r(y,{style:{"overflow-y":"auto",height:"calc(100vh - 65px)",padding:"0"}},{default:a(()=>[l("div",Me,[l("div",Ne,[o[4]||(o[4]=l("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}},[l("div",{style:{"font-size":"20px","font-weight":"bold"}},"导出与备份(未完待续...）"),l("div",{style:{display:"flex","justify-content":"space-between","align-items":"center"}})],-1)),l("div",Te,[o[1]||(o[1]=u(" 导出类型: ",-1)),r(i,{placeholder:"请选择导出类型",style:{width:"50%"},modelValue:n.value,"onUpdate:modelValue":o[0]||(o[0]=k=>n.value=k)},{default:a(()=>[(s(),b(O,null,X(f,(k,B)=>r(d,{label:k.brief,value:B,key:B},{default:a(()=>[u(m(k.brief),1)]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"]),o[2]||(o[2]=l("br",null,null,-1)),o[3]||(o[3]=l("br",null,null,-1)),n.value?(s(),b("span",ze,m(f[n.value].detail),1)):c("",!0)]),r(v),n.value=="endb"?(s(),V(Be,{key:0,wxid:x.wxid},null,8,["wxid"])):c("",!0),n.value=="dedb"?(s(),V(Ce,{key:1,wxid:x.wxid},null,8,["wxid"])):c("",!0),n.value=="csv"?(s(),V(qe,{key:2,wxid:x.wxid},null,8,["wxid"])):c("",!0),n.value=="json"?(s(),V(Le,{key:3,wxid:x.wxid},null,8,["wxid"])):c("",!0),n.value=="html"?(s(),V(Ue,{key:4,wxid:x.wxid},null,8,["wxid"])):c("",!0),n.value=="pdf"?(s(),V(De,{key:5,wxid:x.wxid},null,8,["wxid"])):c("",!0),n.value=="docx"?(s(),V(je,{key:6,wxid:x.wxid},null,8,["wxid"])):c("",!0)])])]),_:1})])}}}),Fe=C({__name:"ChatRecords",props:{wxid:{type:String,required:!0}},setup(t){const x=t,n=_(!1),f=o=>{n.value=o},e=()=>{n.value=!1};return I(()=>x.wxid,async(o,d)=>{o!==d&&e()}),z(()=>{e()}),(o,d)=>{const i=p("el-header"),v=p("el-main"),y=p("el-container");return s(),V(y,null,{default:a(()=>[r(i,{style:{height:"40px","max-height":"40px",width:"100%","background-color":"#d2d2fa","padding-top":"5px"}},{default:a(()=>[r(Ee,{wxid:t.wxid,onExporting:f},null,8,["wxid"])]),_:1}),r(v,{style:{height:"calc(100vh - 40px)",padding:"0",margin:"0","background-color":"#f5f5f5"}},{default:a(()=>[n.value?(s(),V(Oe,{key:0,wxid:t.wxid},null,8,["wxid"])):(s(),V(G,{key:1,wxid:t.wxid},null,8,["wxid"]))]),_:1})]),_:1})}}}),He={id:"chat_view",class:"common-layout"},he={key:0,style:{height:"calc(100vh)",width:"100%"}},Pe={key:1,style:{width:"100%",height:"100%"}},Xe=C({__name:"ChatView",setup(t){const x=_("");return z(()=>{Q().then(n=>{console.log("API version: "+n)}).catch(n=>{console.error("Error fetching API version:",n)}),Y()}),(n,f)=>{const e=p("el-aside"),o=p("el-container");return s(),b("div",He,[l("div",null,[r(o,null,{default:a(()=>[r(e,{width:"auto",style:{"overflow-y":"auto",height:"calc(100vh)"}},{default:a(()=>[r(ge,{onWxid:f[0]||(f[0]=d=>{x.value=d})})]),_:1}),x.value!=""?(s(),b("div",he,[r(Fe,{wxid:x.value},null,8,["wxid"])])):(s(),b("div",Pe,[r(Z)]))]),_:1})])])}}});export{Xe as default};
