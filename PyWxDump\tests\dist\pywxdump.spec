# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(['wxdump.py'],
             pathex=[],
             binaries=[],
             datas=[(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\WX_OFFS.json', 'pywxdump'),
            (r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\cli.py', r'pywxdump'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\WX_OFFS.json', r'pywxdump'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\__init__.py', r'pywxdump'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\analyzer\chat_analysis.py', r'pywxdump\analyzer'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\analyzer\cleanup.py', r'pywxdump\analyzer'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\analyzer\utils.py', r'pywxdump\analyzer'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\analyzer\__init__.py', r'pywxdump\analyzer'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\local_server.py', r'pywxdump\api'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\remote_server.py', r'pywxdump\api'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\rjson.py', r'pywxdump\api'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\utils.py', r'pywxdump\api'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\__init__.py', r'pywxdump\api'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\export\exportCSV.py', r'pywxdump\api\export'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\export\exportHtml.py', r'pywxdump\api\export'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\export\exportJSON.py', r'pywxdump\api\export'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\api\export\__init__.py', r'pywxdump\api\export'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbbase.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbFavorite.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbMedia.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbMicro.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbMSG.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbOpenIMContact.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbOpenIMMedia.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbPublicMsg.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\dbSns.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\__init__.py', r'pywxdump\db'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\utils\common_utils.py', r'pywxdump\db\utils'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\utils\_loger.py', r'pywxdump\db\utils'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\db\utils\__init__.py', r'pywxdump\db\utils'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\__init__.py', r'pywxdump\ui'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\data.js', r'pywxdump\ui\web'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\favicon.ico', r'pywxdump\ui\web'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\index.html', r'pywxdump\ui\web'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\AboutView-DbMAziD1.css', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\AboutView-WxyTTUOs.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\BiasView-7_odqyCS.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\ChatRecprdsHeader-DyskaUp2.css', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\ChatView-qP5BrSEn.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\CleanupView-BVDwkJ_A.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\ContactsView-C4wlnWql.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\DateTimeSelect-C1U9dD_5.css', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\DbInitComponent.vue_vue_type_script_setup_true_lang-DzNmdpgv.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\DbInitView-CxpR1ubR.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\DecryptView-DWuPBGsd.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\FavoriteView-breqVoli.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\HelpView-ChCLaiCs.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\HomeView-BGjGzdig.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\index-B2o9x5LI.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\index-D1OdS_Qw.css', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\IndexView-CAPKPbgX.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\IndexView.vue_vue_type_script_setup_true_lang-nwd8pAHf.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\MergeView-DwGdnOw-.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\MomentsView-BgDIYH7V.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\SettingView-fopBzVLK.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\StatisticsView-c2GX90Ia.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\StatisticsView-C2v-j63B.css', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\ui\web\assets\WxinfoView-BgvQJjVZ.js', r'pywxdump\ui\web\assets'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\decryption.py', r'pywxdump\wx_core'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\get_bias_addr.py', r'pywxdump\wx_core'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\memory_search.py', r'pywxdump\wx_core'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\merge_db.py', r'pywxdump\wx_core'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\wx_info.py', r'pywxdump\wx_core'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\__init__.py', r'pywxdump\wx_core'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\tools\libcrypto-1_1-x64.dll', r'pywxdump\wx_core\tools'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\tools\realTime.exe', r'pywxdump\wx_core\tools'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\utils\common_utils.py', r'pywxdump\wx_core\utils'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\utils\ctypes_utils.py', r'pywxdump\wx_core\utils'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\utils\memory_search.py', r'pywxdump\wx_core\utils'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\utils\_loger.py', r'pywxdump\wx_core\utils'),
(r'C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pywxdump\wx_core\utils\__init__.py', r'pywxdump\wx_core\utils')
            ],
             hiddenimports=['pycryptodomex', 'pywin32', 'silk_python', 'pyaudio', 'requests', 'pyahocorasick', 'lz4', 'blackboxprotobuf', 'lxml', 'dbutils', 'psutil', 'pymem', 'pydantic', 'fastapi', 'uvicorn', 'python_dotenv', 'pywxdump', 'pywxdump.db', 'pywxdump.db.__init__.utils'],
             hookspath=[],
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)

version_info = {
          'FileDescription': 'PyWxDump from https://github.com/xaoyaoo/PyWxDump',
          'OriginalFilename': 'None',
          'ProductVersion': '********',  # 版本号
          'FileVersion': '********',
          'InternalName': 'wxdump'
}
a.version = version_info


pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(pyz,
          a.scripts,
          a.binaries,
          a.zipfiles,
          a.datas,
          [],
          name='wxdump',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,  # 启用压缩
          console=True,  # 使用控制台 
          disable_windowed_traceback=True,  # 不禁用堆栈跟踪
          argv_emulation=False, # 不模拟命令行参数
          target_arch=None,  # 自动检测目标 CPU 架构
          codesign_identity=None,  # 不签名应用程序
          entitlements_file=None,  # 不使用 entitlements 文件
          onefile=True,  # 生成单个可执行文件
          icon="icon.ico",
          version='wxdump_version_info.txt'
          )

coll = COLLECT(exe,
               a.binaries,
               a.zipfiles,
               a.datas,
               strip=False,
               upx=True,
               upx_exclude=[],
               name='wxdump')