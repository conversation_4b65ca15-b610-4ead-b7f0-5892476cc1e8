import{d as n,r as a,a as i,b as l,e as o,f as r,t as d,g as p,o as c}from"./index-B2o9x5LI.js";const g={style:{"background-color":"#d2d2fa",height:"100vh",display:"grid","place-items":"center"}},u={style:{"text-align":"center"}},m={style:{"text-align":"center"}},y=n({__name:"FavoriteView",setup(f){const s=a("");return i(()=>{p().then(t=>{s.value=t}).catch(t=>{console.error("Error fetching API version:",t)})}),(t,e)=>(c(),l("div",g,[o("h2",u,[e[0]||(e[0]=r("欢迎使用",-1)),e[1]||(e[1]=o("a",{href:"https://github.com/xaoyaoo/PyWxDump.git"},"PyWxDump",-1)),e[2]||(e[2]=r("聊天记录查看工具! ",-1)),o("p",m,"当前版本："+d(s.value),1)])]))}});export{y as default};
